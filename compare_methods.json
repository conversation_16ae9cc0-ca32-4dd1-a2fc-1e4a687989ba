{"name_race_gender_coords": [{"ds1_fld": "first_name_soundex", "ds2_fld": "first_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_first", "weight": 0.05}, {"ds1_fld": "middle_name_soundex", "ds2_fld": "middle_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_middle", "weight": 0.05}, {"ds1_fld": "last_name_soundex", "ds2_fld": "last_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_last", "weight": 0.05}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.85, "match_fldname": "match_first", "weight": 0.2}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.75, "match_fldname": "match_middle", "weight": 0.05}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.88, "match_fldname": "match_last", "weight": 0.25}, {"ds1_fld": "birth_date", "ds2_fld": "birth_date", "compare_type": "date", "match_fldname": "match_birth_date", "weight": 0.05}, {"ds1_fld": "race", "ds2_fld": "race", "compare_type": "exact", "match_fldname": "match_race", "weight": 0.05}, {"ds1_fld": "gender", "ds2_fld": "gender", "compare_type": "exact", "match_fldname": "match_gender", "weight": 0.05}, {"ds1_fld": ["latitude", "longitude"], "ds2_fld": ["latitude", "longitude"], "compare_type": "geo", "method": "linear", "thresh": 0.85, "match_fldname": "match_coordinate", "weight": 0.2}], "name_race_gender": [{"ds1_fld": "first_name_soundex", "ds2_fld": "first_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_first", "weight": 0.1}, {"ds1_fld": "middle_name_soundex", "ds2_fld": "middle_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_middle", "weight": 0.05}, {"ds1_fld": "last_name_soundex", "ds2_fld": "last_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_last", "weight": 0.1}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.85, "match_fldname": "match_first", "weight": 0.25}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.75, "match_fldname": "match_middle", "weight": 0.05}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.88, "match_fldname": "match_last", "weight": 0.25}, {"ds1_fld": "birth_date", "ds2_fld": "birth_date", "compare_type": "date", "match_fldname": "match_birth_date", "weight": 0.1}, {"ds1_fld": "race", "ds2_fld": "race", "compare_type": "exact", "match_fldname": "match_race", "weight": 0.05}, {"ds1_fld": "gender", "ds2_fld": "gender", "compare_type": "exact", "match_fldname": "match_gender", "weight": 0.05}], "name_race_gender_exact_name": [{"ds1_fld": "first_name_soundex", "ds2_fld": "first_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_first", "weight": 0.05}, {"ds1_fld": "middle_name_soundex", "ds2_fld": "middle_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_middle", "weight": 0.02}, {"ds1_fld": "last_name_soundex", "ds2_fld": "last_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_last", "weight": 0.05}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "exact", "match_fldname": "match_first_exact", "weight": 0.1}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "exact", "match_fldname": "match_middle_exact", "weight": 0.1}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "exact", "match_fldname": "match_last_exact", "weight": 0.2}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.85, "match_fldname": "match_first", "weight": 0.1}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.75, "match_fldname": "match_middle", "weight": 0.05}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.88, "match_fldname": "match_last", "weight": 0.15}, {"ds1_fld": "birth_date", "ds2_fld": "birth_date", "compare_type": "date", "match_fldname": "match_birth_date", "weight": 0.1}, {"ds1_fld": "race", "ds2_fld": "race", "compare_type": "exact", "match_fldname": "match_race", "weight": 0.04}, {"ds1_fld": "gender", "ds2_fld": "gender", "compare_type": "exact", "match_fldname": "match_gender", "weight": 0.04}], "name_race_exact_name_no_dob": [{"ds1_fld": "first_name_soundex", "ds2_fld": "first_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_first", "weight": 0.05}, {"ds1_fld": "middle_name_soundex", "ds2_fld": "middle_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_middle", "weight": 0.02}, {"ds1_fld": "last_name_soundex", "ds2_fld": "last_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_last", "weight": 0.05}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "exact", "match_fldname": "match_first_exact", "weight": 0.12}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "exact", "match_fldname": "match_middle_exact", "weight": 0.1}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "exact", "match_fldname": "match_last_exact", "weight": 0.25}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.85, "match_fldname": "match_first", "weight": 0.12}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.75, "match_fldname": "match_middle", "weight": 0.05}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.88, "match_fldname": "match_last", "weight": 0.2}, {"ds1_fld": "race", "ds2_fld": "race", "compare_type": "exact", "match_fldname": "match_race", "weight": 0.04}], "name_only": [{"ds1_fld": "first_name_soundex", "ds2_fld": "first_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_first", "weight": 0.05}, {"ds1_fld": "middle_name_soundex", "ds2_fld": "middle_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_middle", "weight": 0.02}, {"ds1_fld": "last_name_soundex", "ds2_fld": "last_name_soundex", "compare_type": "exact", "match_fldname": "match_sdx_last", "weight": 0.05}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "exact", "match_fldname": "match_first_exact", "weight": 0.13}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "exact", "match_fldname": "match_middle_exact", "weight": 0.1}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "exact", "match_fldname": "match_last_exact", "weight": 0.26}, {"ds1_fld": "first_name_clean", "ds2_fld": "first_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.85, "match_fldname": "match_first", "weight": 0.13}, {"ds1_fld": "middle_name_clean", "ds2_fld": "middle_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.75, "match_fldname": "match_middle", "weight": 0.05}, {"ds1_fld": "last_name_clean", "ds2_fld": "last_name_clean", "compare_type": "string", "method": "<PERSON><PERSON><PERSON><PERSON>", "thresh": 0.88, "match_fldname": "match_last", "weight": 0.21}]}