# import ScitKit-Learn classifier
from sklearn.ensemble import RandomForestClassifier
from recordlinkage.base import BaseClassifier
from recordlinkage.adapters import SKLearnAdapter


class RandomForest(SKLearnAdapter, BaseClassifier):

    def __init__(self, *args, **kwargs):
        super(SKLearnAdapter).__init__()
        super(BaseClassifier).__init__()

        # set the kernel
        self.kernel = RandomForestClassifier(*args, **kwargs)

    def set_results(self,df,true_links,result_fld,pred_links,pred_fld):
        df.loc[true_links,result_fld] = 1
        df[result_fld].fillna(0,inplace=True)
        df.loc[pred_links,pred_fld] = 1
        df[pred_fld].fillna(0,inplace=True)
        return df


