import itertools
import numpy as np
import pandas as pd
import polars
import polars as pl
import pprint
import random
import recordlinkage
from recordlinkage.preprocessing import clean,phonetic

class RecordLinkage():
    
    def __init__(self, 
                 unique_key_name='unique_pk', 
                 dedup=False):
        
        self.unique_key_name = unique_key_name
        self.dedup=dedup

        self.candidates = None
        self.features = None
        self.weights = None
    
    def assign_datasources(self,
                           ds1, 
                           ds2, 
                           ds1_unique_key, 
                           ds2_unique_key, 
                           ds1_dataset_key, 
                           ds2_dataset_key,
                           ds1_alternate_key=None, 
                           ds2_alternate_key=None):
        self.ds1 = ds1
        self.ds2 = ds2
        self.ds1_unique_key = ds1_unique_key
        self.ds2_unique_key = ds2_unique_key
        self.ds1_dataset_key = ds1_dataset_key
        self.ds2_dataset_key = ds2_dataset_key
        self.ds1_alternate_key = ds1_alternate_key
        self.ds2_alternate_key = ds2_alternate_key
    
    
    @staticmethod
    def assign_unique_key(df,key_fld,unique_key_name='unique_pk'):
        df = (df
            .with_columns(
                pl.col(key_fld)
                .cum_count()
                .over(key_fld)
                .cast(pl.Utf8)
                .alias('counter')
            )
            .with_columns(
                pl.concat_str([pl.col(key_fld),pl.col("counter")],separator='_')
                .alias(unique_key_name)
            )
            .drop('counter')
        )
        return df
    
    @staticmethod
    def rename_columns(df,**kwargs):
        df = df.rename({v:k for k,v in kwargs.items()})
        return df
    
    @staticmethod
    def create_dataset_dict(ds_name, df, dataset_key, unique_key, alternate_key=None, name_fields = ['first_name','middle_name','last_name']):
        # add prefix
        df.columns = [f"{ds_name}_{col}" for col in df.columns]
        name_fields = [f"{ds_name}_{n}" for n in name_fields]
        ds = {
            ds_name: {
                "datasource":df,
                "name_fields": name_fields,
                "unique_key": f"{ds_name}_{unique_key}",
                "dataset_key": f"{ds_name}_{dataset_key}",
                "alternate_key": f"{ds_name}_{alternate_key}" if  alternate_key else None
            }
        }
        
        # run preprocessing
        for k in ds.keys():
            ds_df = ds[k]['datasource']
            for name in ds[k]['name_fields']:
                # clean names and perform soundex encoding
                ds_df = RecordLinkage.preprocess(ds_df,
                                         name,
                                        f'{name}_clean',
                                        f'{name}_soundex',
                                        phonetic_method='soundex')
            
            ds_df.set_index(f"{k}_{unique_key}",inplace=True)
            ds[k]['datasource'] = ds_df
        return ds
    
    @staticmethod                
    def preprocess(df, fldname, clean_fldname, phonetic_fldname ,phonetic_method='soundex'):
        
        try:
            df[clean_fldname] = clean(df[fldname])
        except:
            df[clean_fldname] = df[fldname]
            
        # make sure the clean field name is a string
        df[clean_fldname] = df[clean_fldname].astype(str)
            
        df[phonetic_fldname] = phonetic(df[clean_fldname],method=phonetic_method)
        
        return df
    
    @staticmethod
    def get_combos(ds_keys: list, main_ds: list=[]):
        _main_ds = main_ds
        
        # if there is only 1 element in the main ds remove from the ds_keys
        if len(_main_ds) == 1 and len(ds_keys) > 1:
            _ds_keys = list([k for k in ds_keys if k not in _main_ds])
        else:
            _ds_keys = ds_keys
            
        # if there is only one main dataset and there are ds keys get the 
        # product of the main dataset and ds keys as the combos
        if len(_main_ds) == 1 and len(_ds_keys) >= 1:
            rl_combos = list(itertools.product(_main_ds,_ds_keys))
        # if there is only one main dataset and no ds keys it will be deduplication
        elif len(_main_ds) == 1 and len(_ds_keys) == 0:
            rl_combos = [(_main_ds[0],_main_ds[0])]
        # otherwise get all teh two dataset combinations between the ds keys
        else:
            rl_combos = list(itertools.combinations(_ds_keys,2))
            # if there are more than one main datasets filter out combinations 
            # that are not related to one of the  main datasets
            if len(_main_ds) > 1:
                rl_combos = [c for c in rl_combos if c[0] in _main_ds or c[1] in _main_ds]
                
        return rl_combos
    
    @staticmethod    
    def get_rl_combinations(ds,main_ds=[],dedup=False):
        rl_dict = {}
        
        rl_combos = RecordLinkage.get_combos(ds_keys=ds.keys(), main_ds=main_ds)
        print(rl_combos)
        
        for rlc in rl_combos:
            rl_name = f"{'_'.join(rlc)}"
            print(rl_name)
            
            ds1 = ds[rlc[0]]['datasource'].copy()
            ds1_unique_key = ds[rlc[0]]['unique_key']
            ds1_dataset_key = ds[rlc[0]]['dataset_key']
            ds1_alternate_key = ds[rlc[0]]['alternate_key']
            
            ds2 = ds[rlc[1]]['datasource'].copy()
            ds2_unique_key = ds[rlc[1]]['unique_key']
            ds2_dataset_key = ds[rlc[1]]['dataset_key']
            ds2_alternate_key = ds[rlc[1]]['alternate_key']
            
            if dedup:
                ds1.reset_index(inplace=True)
                ds1[f"{ds1_unique_key}_1"] = ds1[ds1_unique_key]
                ds1[f"{ds1_dataset_key}_1"] = ds1[ds1_dataset_key]
                if ds1_alternate_key:
                    ds1[f"{ds1_alternate_key}_1"] = ds1[ds1_alternate_key]
                    ds1.drop([ds1_alternate_key],axis=1,inplace=True)
                ds1.drop([ds1_unique_key,ds1_dataset_key],axis=1,inplace=True)
                               
                ds1_unique_key,ds1_dataset_key = [f"{ds1_unique_key}_1",f"{ds1_dataset_key}_1"]
                if ds1_alternate_key:
                    ds1_alternate_key = f"{ds1_alternate_key}_1"
                    
                ds1.set_index(ds1_unique_key,inplace=True)
                
                
                ds2.reset_index(inplace=True)
                ds2[f"{ds2_unique_key}_2"] = ds2[ds2_unique_key]
                ds2[f"{ds2_dataset_key}_2"] = ds2[ds2_dataset_key]
                if ds2_alternate_key:
                    ds2[f"{ds2_alternate_key}_2"] = ds2[ds2_alternate_key]
                    ds2.drop([ds2_alternate_key],axis=1,inplace=True)
                ds2.drop([ds2_unique_key,ds2_dataset_key],axis=1,inplace=True)
                
                ds2_unique_key,ds2_dataset_key = [f"{ds2_unique_key}_2", f"{ds2_dataset_key}_2"]
                if ds2_alternate_key:
                    ds2_alternate_key = f"{ds2_alternate_key}_2"
                ds2.set_index(ds2_unique_key,inplace=True)

            rl = RecordLinkage(dedup=dedup)
            rl.assign_datasources(ds1=ds1,ds2=ds2,
                                ds1_unique_key=ds1_unique_key,ds2_unique_key=ds2_unique_key,
                                ds1_dataset_key=ds1_dataset_key,ds2_dataset_key=ds2_dataset_key,
                                ds1_alternate_key=ds1_alternate_key,ds2_alternate_key=ds2_alternate_key)
            rl_dict[rl_name]= {
                        'name': rl_name,
                        'rl': rl,
                        'rl_ds1':rlc[0],
                        'rl_ds1_unique_key':ds1_unique_key,
                        'rl_ds1_dataset_key':ds1_dataset_key,
                        'rl_ds1_alternate_key':ds1_alternate_key,
                        'rl_ds2':rlc[1],
                        'rl_ds2_unique_key':ds2_unique_key,
                        'rl_ds2_dataset_key':ds2_dataset_key,
                        'rl_ds2_alternate_key':ds2_alternate_key,
                        }
            
        return rl_dict
    
    def sorted_neighbors(self,ds1, ds2=None, left_on=None, right_on=None, block_left_on=None, block_right_on=None, window_size=9):
        indexer = recordlinkage.Index()
        if self.dedup:
            indexer.sortedneighbourhood(
                left_on=left_on,
                block_on=block_left_on,
                window=window_size
            )
            self.candidates = indexer.index(ds1)
        else:
            indexer.sortedneighbourhood(
                left_on=left_on, 
                right_on=right_on, 
                window=window_size,
                block_left_on=block_left_on, 
                block_right_on=block_right_on
            )
            self.candidates = indexer.index(ds1, ds2)
            
    @staticmethod
    def run_sorted_neighborhood_indexing(rl_key, ds, window_size=9):
        print(f"Record Linkage Indexing for {rl_key['name']}")
        rl_key['rl'].sorted_neighbors(
                            ds[rl_key['rl_ds1']]['datasource'],
                            ds[rl_key['rl_ds2']]['datasource'],
                            left_on=f"{rl_key['rl_ds1']}_first_name_soundex", 
                            right_on=f"{rl_key['rl_ds2']}_first_name_soundex",
                            block_left_on=[f"{rl_key['rl_ds1']}_birth_date"],
                            block_right_on=[f"{rl_key['rl_ds2']}_birth_date"],
                            window_size=window_size
                        )
        fsdx_bd_cand = rl_key['rl'].candidates
        print(f'Candidates using Sorted Neighborhood using Soundex First Name and Birthdate: {len(fsdx_bd_cand)}')
        
        rl_key['rl'].sorted_neighbors(
                            ds[rl_key['rl_ds1']]['datasource'],
                            ds[rl_key['rl_ds2']]['datasource'],
                            left_on=f"{rl_key['rl_ds1']}_last_name_soundex", 
                            right_on=f"{rl_key['rl_ds2']}_last_name_soundex",
                            block_left_on=[f"{rl_key['rl_ds1']}_birth_date"],
                            block_right_on=[f"{rl_key['rl_ds2']}_birth_date"],
                            window_size=window_size
                        )
        lsdx_bd_cand = rl_key['rl'].candidates
        print(f'Candidates using Sorted Neighborhood using Soundex Last Name and Birthdate: {len(lsdx_bd_cand)}')

        rl_key['rl'].candidates = fsdx_bd_cand.union(lsdx_bd_cand)
        print(f"Combined Possible Candidates using Sorted Neighborhood: {len(rl_key['rl'].candidates)}\n")
        
    def set_comparison_fields(comp_flds,ds1,ds2):
        cp = []
        for fld in comp_flds:
            _fld = fld.copy()
            if _fld['compare_type'] == 'geo':
                _fld["left_on_lat"] = f"{ds1}_{_fld['ds1_fld'][0]}"
                _fld["left_on_lng"] = f"{ds1}_{_fld['ds1_fld'][1]}"
                _fld["right_on_lat"] = f"{ds2}_{_fld['ds2_fld'][0]}"
                _fld["right_on_lng"] = f"{ds2}_{_fld['ds2_fld'][1]}"
                del _fld["ds1_fld"], _fld["ds2_fld"]
            else:
                _fld['ds1_fld'] = f"{ds1}_{_fld['ds1_fld']}"
                _fld['ds2_fld'] = f"{ds2}_{_fld['ds2_fld']}"
            cp.append(_fld)
        return cp
    
    def compare_records(self,compare_fields):
        self.match_fields = [x['match_fldname'] for x in compare_fields]
        print('Match Fields:',self.match_fields)
        
        # calculate the weighting of match fields
        try:
            # create list of weights
            self.weights = [x['weight'] for x in compare_fields]
            # get the sum of weights
            weighted_sum = round(np.sum(self.weights),2)
            assert weighted_sum == 1.0, 'weighted sum should equal 1'
        except TypeError:
            self.weights = None
            print('Type Error')
        except Exception as e:
            self.weights = None
            print(e)
        finally:
            if self.weights:
                self.weights_map = {x['match_fldname']:x['weight'] for x in compare_fields}
                pprint.pprint(f"""Weights: {self.weights_map}""")
            else:
                pprint.pprint(f"""Weights: {self.weights}""")
                
        # store geo threshold in seperate variable
        geo_thresh = None
        self.compare = recordlinkage.Compare()
        for cf in compare_fields:
            if cf['compare_type'] == 'exact':
                ds1_fld, ds2_fld, _, match_fldname, _ = list(cf.values())
                self.compare.exact(ds1_fld, ds2_fld)
            elif cf['compare_type'] == 'string':
                ds1_fld, ds2_fld, _, method, thresh, match_fldname, _ = list(cf.values())
                self.compare.string(ds1_fld, ds2_fld, method=method, threshold=thresh)
            elif cf['compare_type'] == 'date':
                ds1_fld, ds2_fld, _, match_fldname, _ = list(cf.values())
                self.compare.date(ds1_fld, ds2_fld)
            elif cf['compare_type'] == 'geo':
                _, method, thresh, match_fldname, _, ds1_lat, ds1_lng, ds2_lat, ds2_lng = list(cf.values())
                # set geo thresh
                geo_thresh = thresh
                geo_fldname = match_fldname
                self.compare.geo(left_on_lat=ds1_lat,
                                 left_on_lng=ds1_lng,
                                 right_on_lat=ds2_lat,
                                 right_on_lng=ds2_lng,
                                 method=method,
                                )
                
        
        if self.dedup:
            self.comps = self.compare.compute(self.candidates,self.ds1)
        else:
            self.comps = self.compare.compute(self.candidates,self.ds1,self.ds2)
        
        self.comps.columns = self.match_fields
        # threshold doesn't appear to be set in compare method like string similarity, 
        # so it will be set after compute
        if geo_thresh:
            self.comps[geo_fldname] = np.where(self.comps[geo_fldname] > geo_thresh, 1,0)

        print(f"There are {len(self.comps):,} matching candidates")
        
        
    def predict(self,clf):
        self.preds = clf.predict(self.comps)
        # store predictions
        self.comps = self.set_predictions(self.comps,self.preds)
        
    def classify(self, clf, labels=None):
        self.preds = clf.fit_predict(self.comps)
        # store predictions
        self.comps = self.set_predictions(self.comps,self.preds)
        
    def set_predictions(self,df,predictions,pred_fld='match_pred'):
        df[pred_fld] = 0
        df.loc[predictions,pred_fld] = 1
        print(f'There were {len(predictions):,} out of {len(df):,} candidates ({len(predictions)/len(df):.0%}) predicted as matches')
        return df
    
    def calc_match_score(self, exclude_cols=['match_pred','true_match']):
        match_cols = [col for col in self.comps.columns \
            if col not in exclude_cols]
        if self.weights:
            self.comps['match_score'] = \
                round(self.comps[self.match_fields].apply(self.calc_weighted_match_score,axis=1),2)
        else:
            self.comps['match_score'] = \
                round(self.comps[self.match_fields].sum(axis=1)/self.comps[self.match_fields].shape[1],2)

        if (self.ds1_dataset_key or self.ds2_dataset_key):
                self.comps = (self.comps
                                .reset_index()
                                # get secondary index for ds1
                                .merge(self.ds1[[self.ds1_dataset_key]],
                                left_on=self.ds1_unique_key,
                                right_on=self.ds1_unique_key,
                                how='left')
                                # get secondary index for ds2
                                .merge(self.ds2[[self.ds2_dataset_key]],
                                left_on=self.ds2_unique_key,
                                right_on=self.ds2_unique_key,
                                how='left')
                                )
                
    def calc_match_score2(self, exclude_cols=['match_pred','true_match']):
        comps = pl.from_pandas(self.comps,include_index=True)
        exclude_cols=[self.ds1_unique_key,self.ds2_unique_key,'match_pred','true_match']
        match_cols = [col for col in self.comps.columns if col not in exclude_cols]
        weight_cols = [f"{k}_w" for k in self.weights_map.keys()]
        
        if self.weights:
            self.comps = (comps
                .with_columns(
                    [(pl.col(k) * pl.lit(v)).alias(f"{k}_w") for k,v in self.weights_map.items()]
                )
                .with_columns(
                    pl.sum_horizontal(weight_cols).round(2).alias('match_score')
                )
                .drop([f"{k}_w" for k in self.weights_map.keys()])
                .to_pandas()
                .set_index([self.ds1_unique_key,self.ds2_unique_key])
            )
            
        else:
            self.comps = (comps
                            .with_columns(
                                (pl.sum_horizontal(match_cols).alias('match_score')/len(match_cols)).round(2)
                            )
                            .to_pandas()
                            .set_index([self.ds1_unique_key,self.ds2_unique_key])
                        )

        if (self.ds1_dataset_key or self.ds2_dataset_key):
                self.comps = (self.comps
                                .reset_index()
                                # get secondary index for ds1
                                .merge(self.ds1[[self.ds1_dataset_key]],
                                left_on=self.ds1_unique_key,
                                right_on=self.ds1_unique_key,
                                how='left')
                                # get secondary index for ds2
                                .merge(self.ds2[[self.ds2_dataset_key]],
                                left_on=self.ds2_unique_key,
                                right_on=self.ds2_unique_key,
                                how='left')
                                )
    
    def calc_weighted_match_score(self,x):
        return np.sum([(x[fld] * self.weights_map[fld]) \
            for fld in self.weights_map.keys()])    
        
    def get_score_pred_summary(self):
        scores = sorted([p for p in sorted(self.comps.match_score.unique())])
        predictions = [self.comps.query(f"match_score == {p}").match_pred.unique().tolist() for p in scores]
        rows = [[self.comps.query(f"match_score == {s[0]} and match_pred == {p}").shape[0] for p in s[1]] for s in zip(scores,predictions)]

        self.scores_pred = pd.DataFrame({
        'match_score':scores,
        'predictions':predictions,
        'number_rows':rows
        })

        self.scores_review = self.scores_pred.match_score[[i for i,x in enumerate(self.scores_pred.predictions) if len(x)==2]].tolist()
        
    def set_matches(self, match_query):
        self.comps_match = self.comps.query(match_query).copy()
        print(f"There are {self.comps_match.shape[0]:,} matches")
        
    def process_duplicates(self,match_score_fld='match_score'):
        comps_match = pl.from_pandas(self.comps_match)
        print('\nChecking Duplicates')
        
        comps_match = RecordLinkage.remove_duplicates(comps_match,
                                        ds1_key=self.ds1_unique_key,
                                        ds2_key=self.ds2_unique_key,
                                        match_score_fld=match_score_fld)
        
        comps_match = RecordLinkage.remove_duplicates(comps_match,
                                        ds1_key=self.ds2_unique_key,
                                        ds2_key=self.ds1_unique_key,
                                        match_score_fld=match_score_fld)
        
        comps_match = RecordLinkage.remove_duplicates(comps_match,
                                        ds1_key=self.ds1_dataset_key,
                                        ds2_key=self.ds2_dataset_key,
                                        match_score_fld=match_score_fld)
        
        comps_match = RecordLinkage.remove_duplicates(comps_match,
                                        ds1_key=self.ds2_dataset_key,
                                        ds2_key=self.ds1_dataset_key,
                                        match_score_fld=match_score_fld)
        return (comps_match
                .unique(subset=[self.ds1_dataset_key,self.ds2_dataset_key])
                .select(pl.col(self.ds1_dataset_key,self.ds2_dataset_key,match_score_fld))
                )
    
    def remove_duplicates(df,ds1_key,ds2_key,match_score_fld='match_score'):
        dups = (df
                .filter(pl.col(ds1_key).is_duplicated())
                .sort(by=[ds1_key,match_score_fld],
                      descending=[False,True]))
        if dups.height > 0:
            print(f"""\nFound {dups.height} duplicate records between {ds1_key} and {ds2_key}""")
            dups.group_by([ds1_key,match_score_fld]).agg(pl.col(ds2_key))
            print('Number of Total Records Before Deduplication:',df.height)
            print(f'Number of Total Unique Keys in {ds1_key} Before Deduplication:',df[ds1_key].n_unique())
            print('Number of Duplicate Records:',dups.height)
            print(f'Number of Duplicated Unique Keys in {ds1_key}:',dups[ds1_key].n_unique())
        
            dups_agg = (dups
                        .group_by([ds1_key,match_score_fld])
                        .agg(pl.col(ds2_key))
                        .sort([ds1_key,match_score_fld],descending=[False,True])
            )
            print(f'Data grouped by {ds1_key} and sorted by highest match score.')
            
            dups_keep = (dups_agg
                .unique(subset=ds1_key,keep='first')
                .sort(by=[ds1_key])
                .explode(ds2_key).drop(match_score_fld))
            print(f'Keeping Highest Scoring Records for each {ds1_key} key')
            print(f'Number of Records to Keep:',dups_keep.height)
            print(f'Number of Unique Keys in {ds1_key} to Keep:',dups_keep[ds1_key].n_unique())
            
            dups_remove = (dups
                        .join(dups_keep,how='anti',on=[ds1_key,ds2_key])
                        .select(pl.col(ds1_key,ds2_key)))
            print(f'Number of Records to Remove:',dups_remove.height)
            print(f'Number of Unique Keys in {ds1_key} to Remove:',dups_remove[ds1_key].n_unique())
            
            df = df.join(dups_remove,how='anti',on=[ds1_key,ds2_key])
            print('Number of Removed Records in Dataset After Deduplication:',df.join(dups_remove,on=[ds1_key,ds2_key]).height)
            print('Number of Kept Records in Dataset After Deduplication:',df.join(dups_keep,on=[ds1_key,ds2_key]).height)
            print('Number of Total Records After Deduplication:',df.height)
            print(f'Number of Total Unique Keys in {ds1_key} After Deduplication:',df[ds1_key].n_unique())
        return df

    def get_duplicates_same_score(df,subset_key,match_score_fld):
        # if not polars dataframe convert to one
        if type(df) != polars.dataframe.frame.DataFrame:
            df = pl.from_pandas(df)
            
        return (df
                .filter(pl.col(subset_key).is_duplicated())
                .sort(by=[subset_key,match_score_fld],
                      descending=[False,True])
                .drop_nulls(subset=subset_key)
                )
        
    def get_duplicates_same_score2(df,subset_key):
        # if not polars dataframe convert to one
        if type(df) != polars.dataframe.frame.DataFrame:
            df = pl.from_pandas(df)
            
        return (df
                .filter(pl.col(subset_key).is_duplicated())
                .sort(by=[subset_key],
                      descending=[False])
                .drop_nulls(subset=subset_key)
                )
        
    def assign_duplicates_same_index(all_rec_df,dups_df,subset_key,index_key='index'):
        # if polars dataframe convert to pandas
        if type(all_rec_df) == polars.dataframe.frame.DataFrame:
            all_rec_df = all_rec_df.to_pandas()
        
        if type(dups_df) == polars.dataframe.frame.DataFrame:
            dups_df = dups_df.to_pandas()
            
        
        # get count information about the number of rows and index keys in the all 
        # recs and dups dataframes
        orig_all_rows = all_rec_df.shape[0]
        orig_all_idx_count = all_rec_df[index_key].nunique()
        dup_rows = dups_df.shape[0]
        dup_idx_count = dups_df[index_key].nunique()
        
        # create dataframe of no duplicates
        df_nodups = (all_rec_df
                    .merge(dups_df,
                            how='outer',
                            indicator=True)
                    .query("_merge == 'left_only'")
                    .drop('_merge',axis=1)
                    )
        
        # create dataframe of indexs to keep
        dup_keep_idx = dups_df.drop_duplicates(subset=[subset_key],keep='first')[[index_key,subset_key]]
        # revised dataframe with the indexes updated
        df_dups_rev = (dups_df
                       .drop([index_key],axis=1)
                       .merge(dup_keep_idx,how='left')
                       )
        # concatenate the revised duplicates and no dups data frame together
        df_final = pd.concat([df_dups_rev,df_nodups],ignore_index=True)
        
        # get count information about the number rows and index keys in teh final dataframe
        final_rows = df_final.shape[0]
        final_idx_count = df_final[index_key].nunique()
        
        print(f'Removing duplicates of {subset_key} field complete')
        print(f'Rows before removing duplicates: {orig_all_rows:,}')
        print(f'Number of unique index values before removing duplicates: {orig_all_idx_count:,}')
        print(f'Duplicate rows: {dup_rows}')
        print(f'Number of unique index values in duplicates: {dup_idx_count:,}')
        print(f'Rows after removing duplicates: {final_rows:,}')
        print(f'Number of unique index values after removing duplicates: {final_idx_count:,}')
        
        return pl.from_pandas(df_final)
    
    def assign_duplicates_same_index2(all_rec_df,subset_key,index_key='index'):
        # if polars dataframe convert to pandas
        if type(all_rec_df) != polars.dataframe.frame.DataFrame:
            all_rec_df = pl.from_pandas(all_rec_df)
        
        dups_df = RecordLinkage.get_duplicates_same_score2(all_rec_df,
                                               subset_key=subset_key)
        dup_rows = dups_df.height
        dup_idx_count = dups_df[index_key].n_unique()
        
        # get count information about the number of rows and index keys in the all 
        # recs and dups dataframes
        orig_all_rows = all_rec_df.height
        orig_all_idx_count = all_rec_df[index_key].n_unique()
        
        unique_df = (all_rec_df
                    .unique(subset=subset_key,
                            keep='first')
                    .select(pl.col(subset_key,index_key))
                    .filter(pl.col(subset_key).is_not_null())
                    )
        
        all_rec_df = (all_rec_df
         .join(unique_df, on=subset_key, how='full')
         .with_columns(
            pl.when(pl.col(f'{index_key}_right').is_not_null())
            .then(pl.col(f'{index_key}_right'))
            .otherwise(pl.col(index_key)).alias(index_key)
            )
         .sort(by=subset_key)
         .drop([f'{subset_key}_right',f'{index_key}_right']) 
        )
        
        # get count information about the number rows and index keys in teh final dataframe
        final_rows = all_rec_df.height
        final_idx_count = all_rec_df[index_key].n_unique()
        print('\n')
        print(f'Removing duplicates of {subset_key} field complete')
        print(f'Rows before removing duplicates: {orig_all_rows:,}')
        print(f'Number of unique index values before removing duplicates: {orig_all_idx_count:,}')
        print(f'Duplicate rows: {dup_rows:,}')
        print(f'Number of unique index values in duplicates: {dup_idx_count:,}')
        
        print(f'Rows after removing duplicates: {final_rows:,}')
        print(f'Number of unique index values after removing duplicates: {final_idx_count:,}')
        
        return all_rec_df
    
    @staticmethod           
    def generate_isc_ids(df, id_fld, sort_ascending=True, sort_na_position='last'):
        if type(df) == polars.dataframe.frame.DataFrame:
            df = df.to_pandas()
        
        ids_sorted = (pd.DataFrame(df[id_fld]
                                   .drop_duplicates()
                                   .sort_values(ignore_index=True,
                                                ascending=sort_ascending,
                                                na_position=sort_na_position).copy())
                      )
        len_isc_id = len(ids_sorted)
        start_isc_id = 2 * len_isc_id
        end_isc_id = 7 * len_isc_id
        print(f'Range of Possible IDs\nStart: {start_isc_id}, End: {end_isc_id}')

        isc_ids = random.sample(range(start_isc_id,end_isc_id),len_isc_id)

        ids_sorted['isc_id'] = isc_ids
        return pl.from_pandas(ids_sorted)
    
    @staticmethod
    def get_review_data(df,prefix,index_fld,review_flds):
        if type(df) != polars.dataframe.frame.DataFrame:
            df = pl.from_pandas(df)
        select_flds = [index_fld] + [f"{prefix}_{col}" for col in review_flds]
        return df.select(pl.col(select_flds))
    
    @staticmethod
    def expand_multiple_values_to_new_rec(df,original_column,list_column):
        """In the data frame take the original column and the list column
        and concatenate them into a list so that is has all the values
        represented in the list. Remove the original column and then explode
        the list column so that every element in the list is in a new row and
        finally rename the list column to the original column name and return
        the data frame.

        Args:
            df (Data Frame): The data frame that will be transformed.
            original_column (str): Column name
            list_column (List<str>): List generated from original_column

        Returns:
            Data Frame: Data frame with the records exploded based on the
            list_column.
        """
        return (df
            .with_columns(pl.concat_list([pl.col(original_column),
                                          pl.col(list_column)])
                          .alias(list_column))
            .drop([original_column])
            .explode(list_column)
            .unique()
            .rename({list_column:original_column})
        )
    
    def run_negative_sampling_process(self):
        existing_candidates = self.candidates
        positive_samples = [(x[0].split('_')[0],x[1].split('_')[0])\
            for x in list(self.candidates) \
                if x[0].split('_')[0] == x[1].split('_')[0]]
        existing_neg_samples = [(x[0].split('_')[0],x[1].split('_')[0]) \
            for x in list(self.candidates) \
                if x[0].split('_')[0] != x[1].split('_')[0]]
        
        # if there are to many negative samples reduce to match positive samples
        if len(positive_samples) <= len(existing_neg_samples):
            pos_candidates = [(x[0],x[1])\
            for x in list(self.candidates) \
                if x[0].split('_')[0] == x[1].split('_')[0]]
            neg_candidates = [(x[0],x[1]) \
            for x in list(self.candidates) \
                if x[0].split('_')[0] != x[1].split('_')[0]]
            existing_neg_samples = random.choices(neg_candidates,k=len(pos_candidates))
            neg_samples_mi = pd.MultiIndex.from_tuples(existing_neg_samples,
                                                       names=[self.ds1_unique_key,
                                                              self.ds2_unique_key])
            pos_samples_mi = pd.MultiIndex.from_tuples(pos_candidates,
                                                       names=[self.ds1_unique_key,
                                                              self.ds2_unique_key])
            self.candidates = pos_samples_mi.union(neg_samples_mi)
            
        neg_samples_to_generate = len(positive_samples) - len(existing_neg_samples)
        total_number_candidates = len(positive_samples) + len(existing_neg_samples) + neg_samples_to_generate
        
        print(f"There are {len(positive_samples):,} positive samples.")
        print(f"There are {len(existing_neg_samples):,} existing negative samples.")
        print(f"There are {neg_samples_to_generate:,} negative samples needed to balance out positive samples.")
        print(f"The final candidate list should be have {total_number_candidates:,} candidates.")
        
        if  neg_samples_to_generate > 0:
            ds1_keys = self.ds1[self.ds1_dataset_key].unique().tolist()
            ds2_keys = self.ds2[self.ds2_dataset_key].unique().tolist()
            
            negative_samples = \
                RecordLinkage.generate_negative_samples_for_training(
                    ds1_ids=ds1_keys,
                    ds2_ids=ds2_keys,
                    positive_samples=positive_samples,
                    n_samples=neg_samples_to_generate)
                
            negative_samples_mi = \
                pd.MultiIndex.from_tuples(negative_samples,
                                        names=[self.ds1_unique_key,
                                                self.ds2_unique_key])
                
            self.candidates = existing_candidates.union(negative_samples_mi)
            
        
    @staticmethod
    def generate_negative_samples_for_training(ds1_ids, 
                                               ds2_ids, 
                                               positive_samples, 
                                               n_samples):
        negatives = set()
        attempts = 0
        max_attempts = n_samples * 10  # avoid infinite loops

        while len(negatives) < n_samples and attempts < max_attempts:
            a = random.choice(ds1_ids)
            b = random.choice(ds2_ids)
            pair = (a, b)
            if (positive_samples is None or pair not in positive_samples) \
                and pair not in negatives:
                negatives.add(pair)
            attempts += 1
        # add _1 to reference the unique key version
        negatives = [(f"{x[0]}_1",f"{x[1]}_1") for x in list(negatives)]
        return list(negatives)
            
            
            
