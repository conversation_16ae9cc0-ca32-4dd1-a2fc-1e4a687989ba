from datetime import datetime
from joblib import load
import json
import numpy as np
import os
import plotly.express as px
import polars as pl
pl.Config.set_tbl_rows(30)
import polars.selectors as cs
import pprint
from record_linkage import RecordLinkage
from sklearn.metrics import confusion_matrix, classification_report, f1_score

# set if you want to export the comps data frame for model training
EXPORT_COMPS_FOR_TRAINING = False
# model that the export will be trained for
EXPORT_MODEL_NAME = 'name_race_gender_exact_name'
# ml data folder
EXPORT_MODEL_DATA = 'ml/data'
os.makedirs(EXPORT_MODEL_DATA,exist_ok=True)
# filename to use for training export
EXPORT_COMPS_FOR_TRAINING_FILENAME = f"{EXPORT_MODEL_DATA}/{EXPORT_MODEL_NAME}_{str(datetime.now().strftime('%Y%m%d'))}.csv"
# flag if there is a label with this dataset that can be used for training an assessing accuracy
HAS_LABEL = True
# name of the label field
LABEL_FLD = 'true_match'
# models folder
MODELS_FOLDER = 'ml/models'
# dedup flag
DEDUP = False

ds1_file_path = 'data/main_fake_dataset_50K_with_twins.csv'
ds2_file_path = 'data/sample_fake_dataset_50K_with_twins.csv'

name_fields = ['first_name','middle_name','last_name']

rl = RecordLinkage(dedup=DEDUP)

ds1 = pl.read_csv(ds1_file_path,schema_overrides={'birth_date':pl.Date})
ds1_dataset_key = 'index'

ds1 = ds1.with_columns(pl.col('name_last').str.replace('-',' ').str.strip_chars().str.split(by=' ').alias('name_last_list'))
ds1 = RecordLinkage.expand_multiple_values_to_new_rec(ds1,'name_last','name_last_list')


ds1 = RecordLinkage.assign_unique_key(df=ds1,
                                key_fld=ds1_dataset_key,
                                unique_key_name=rl.unique_key_name)
ds1 = RecordLinkage.rename_columns(ds1,
                             first_name='name_first',
                             middle_name='name_middle',
                             last_name='name_last',
                             birth_date='birth_date',
                             gender='gender',
                             race='bifsg_race',
                             latitude='latitude',
                             longitude='longitude')

ds1 = ds1.to_pandas()
ds1['birth_date'] = ds1['birth_date'].astype('datetime64[ns]')

ds1_ds = RecordLinkage.create_dataset_dict('ds1', ds1, ds1_dataset_key, rl.unique_key_name)

ds1_ds['ds1']['datasource']

ds2 = pl.read_csv(ds2_file_path,schema_overrides={'birth_date':pl.Date})
ds2_dataset_key = 'index'

ds2 = ds2.with_columns(pl.col('name_last').str.replace('-',' ').str.strip_chars().str.split(by=' ').alias('name_last_list'))
ds2 = RecordLinkage.expand_multiple_values_to_new_rec(ds2,'name_last','name_last_list')

ds2 = RecordLinkage.assign_unique_key(df=ds2,
                                key_fld=ds2_dataset_key,
                                unique_key_name=rl.unique_key_name)
ds2 = RecordLinkage.rename_columns(ds2,
                             first_name='name_first',
                             middle_name='name_middle',
                             last_name='name_last',
                             birth_date='birth_date',
                             gender='gender',
                             race='bifsg_race',
                             latitude='latitude',
                             longitude='longitude')
ds2 = ds2.to_pandas()
ds2['birth_date'] = ds2['birth_date'].astype('datetime64[ns]')
ds2_ds = RecordLinkage.create_dataset_dict('ds2', ds2, ds2_dataset_key, rl.unique_key_name)

ds2_ds['ds2']['datasource']

ds2_ds['ds2']['datasource']['ds2_sample_modified_code'].value_counts()

ds = {**ds1_ds, **ds2_ds}
rl_dict = RecordLinkage.get_rl_combinations(ds=ds,main_ds=['ds1'],dedup=DEDUP)
pprint.pprint(rl_dict)

for rl_key in rl_dict.keys():
    RecordLinkage.run_sorted_neighborhood_indexing(rl_dict[rl_key],ds,window_size=9)

if EXPORT_COMPS_FOR_TRAINING:
    for rl_key in rl_dict.keys():
        rl_dict[rl_key]['rl'].run_negative_sampling_process()

rl_dict['ds1_ds2']['rl'].candidates

with open('compare_methods.json','r') as cm:
    compare_methods = json.load(cm)
compare_methods.keys()

rl_dict['ds1_ds2']['compare_name'] = "name_race_gender_exact_name"

for rl_key in rl_dict:
    print(rl_key)
    compare_name = rl_dict[rl_key]['compare_name']
    compare_flds = compare_methods[compare_name]
    
    print(f'\nRecord Linkage: {rl_key}')
    match_flds = [x['match_fldname'] for x in compare_flds]
    
    rl_dict[rl_key]['compare_fields'] = (
        RecordLinkage.set_comparison_fields(compare_flds,
        rl_dict[rl_key]['rl_ds1'],
        rl_dict[rl_key]['rl_ds2'])
        )
    
    rl_dict[rl_key]['rl'].compare_records(rl_dict[rl_key]['compare_fields'])

if EXPORT_COMPS_FOR_TRAINING:
    ds1_unique_key = rl_dict[rl_key]['rl_ds1_unique_key']
    ds1_dataset_key = rl_dict[rl_key]['rl_ds1_dataset_key']
    ds2_unique_key = rl_dict[rl_key]['rl_ds2_unique_key']
    ds2_dataset_key = rl_dict[rl_key]['rl_ds2_dataset_key']
    comps_ml = rl_dict[rl_key]['rl'].comps.copy().reset_index()
    ds1_no_index = ds['ds1']['datasource'][[ds1_dataset_key]].reset_index()
    ds2_no_index = ds['ds2']['datasource'][[ds2_dataset_key]].reset_index()
    comps_ml = (comps_ml
     .merge(ds1_no_index,
            how='left',
            left_on=ds1_unique_key,
            right_on=ds1_unique_key)
     .merge(ds2_no_index,
            how='left',
            left_on=ds2_unique_key,
            right_on=ds2_unique_key))
    comps_ml['true_match'] = np.where(comps_ml[ds1_dataset_key] == comps_ml[ds2_dataset_key],1,0)
    comps_ml.set_index([ds1_unique_key,ds2_unique_key],inplace=True)
    comps_ml.drop([ds1_dataset_key,ds2_dataset_key],axis=1,inplace=True)
    comps_ml.to_csv(EXPORT_COMPS_FOR_TRAINING_FILENAME)

# load random forest classification models
rf_name_dob_gender_race = load(f'{MODELS_FOLDER}/name_dob_gender_race.joblib')
rf_comps_name_race_gender_exact_name = load(f'{MODELS_FOLDER}/name_race_gender_exact_name.joblib')
rf_name_dob_gender_race_coord = load(f'{MODELS_FOLDER}/name_dob_gender_race_coord.joblib')

for key in rl_dict:
    if rl_dict[key]['compare_name'] == 'name_race_gender_coords':
        rl_dict[key]['model'] = rf_name_dob_gender_race_coord
    elif rl_dict[key]['compare_name'] == 'name_race_gender_exact_name':
        rl_dict[key]['model'] = rf_comps_name_race_gender_exact_name
    elif rl_dict[key]['compare_name'] == 'name_race_gender':
        rl_dict[key]['model'] = rf_name_dob_gender_race

for key in rl_dict:
    print(f'\nRecord Linkage: {key}')
    model = rl_dict[key]['model']
    rl_dict[key]['rl'].predict(model)
    rl_dict[key]['rl'].calc_match_score2()
    comps = rl_dict[key]['rl'].comps
    if HAS_LABEL:
        ds_key1 = rl_dict[key]['rl'].ds1_dataset_key
        ds_key2 = rl_dict[key]['rl'].ds2_dataset_key
        rl_dict[key]['rl'].comps[LABEL_FLD] = np.where(rl_dict[key]['rl'].comps[ds_key1] == rl_dict[key]['rl'].comps[ds_key2],1,0)
        rl_dict[key]['rl'].comps['tp'] = np.where(((comps[LABEL_FLD] == 1) & (comps['match_pred'] == 1)),1,0)
        rl_dict[key]['rl'].comps['fn'] = np.where(((comps[LABEL_FLD] == 1) & (comps['match_pred'] == 0)),1,0)
        rl_dict[key]['rl'].comps['tn'] = np.where(((comps[LABEL_FLD] == 0) & (comps['match_pred'] == 0)),1,0)
        rl_dict[key]['rl'].comps['fp'] = np.where(((comps[LABEL_FLD] == 0) & (comps['match_pred'] == 1)),1,0)
        rl_dict[key]['rl'].comps = comps
    del comps

for key in rl_dict:
    print(f'\nRecord Linkage: {key}')
    print(rl_dict[key]['rl'].get_score_pred_summary())
    
    print(rl_dict[key]['rl'].scores_pred.query('match_score >= .5'))
    print(f"Match scores {rl_dict[key]['rl'].scores_review} have predictions for matches and non matches.")
    
    
    scores_pred = rl_dict[key]['rl'].scores_pred.explode(['predictions','number_rows'])
    fig = px.line(scores_pred, 
                x="match_score", 
                y="number_rows", 
                color='predictions',
                color_discrete_sequence=['red','green'],
                title=f'Predictions by Score for {key}',
                labels={
                        "match_score": "Match Score",
                        "number_rows": "Number of Records",
                        "predictions": "Prediction"
                    },
                )
    fig.show()

if HAS_LABEL:
    for key in rl_dict:
        print(f'\nRecord Linkage: {key}')
        cm = confusion_matrix(rl_dict[key]['rl'].comps[LABEL_FLD],
        rl_dict[key]['rl'].comps['match_pred'])
        f1 = f1_score(rl_dict[key]['rl'].comps[LABEL_FLD],
        rl_dict[key]['rl'].comps['match_pred'])
        tn, fp, fn, tp = cm.ravel()
        print(f"""
Confusion Matrix:
[TP:{tp} \t FN:{fn}]
[FP:{fp} \t TN:{tn}]
        
F1-Score: {f1}
""")
        
        print(classification_report(rl_dict[key]['rl'].comps[LABEL_FLD],rl_dict[key]['rl'].comps['match_pred']))

sfpc_list = []
SCORES_BY_FIELD_THRESHOLD = .6
for key in rl_dict:
    comps = rl_dict[key]['rl'].comps
    # create list of match fields
    # match_fields = [c for c in comps.columns if 'match_' in c if c not in ['match_birth_date']]
    match_fields = [x['match_fldname'] for x in rl_dict[key]['compare_fields'] if x['match_fldname'] not in ['match_birth_date']] + ['match_pred','match_score']
    # create new field for aggregation
    comps['number_of_records'] = 1
    # new data frame where the number of records are added up for all the match_field combinations
    scores_by_field = (comps[match_fields+['number_of_records']]
                   .groupby(match_fields)['number_of_records']
                   .sum()
                   .reset_index()
                   .sort_values(['match_score','number_of_records'],ascending=[True, False]))
    # subset of match fields that will be styled, birth_date not needed since all records match
    style_subset = [f for f in match_fields if f not in ['match_birth_date','match_score']]+['number_of_records']
    styler = scores_by_field.query(f"match_score >= {SCORES_BY_FIELD_THRESHOLD}").style.background_gradient(cmap='RdYlGn',subset=style_subset)
    styler.format(precision = 2)
    outpath=f"output/scores_by_field/{key}"
    os.makedirs(outpath,exist_ok=True)
    styler.to_html(f'{outpath}/{key}.html')
    styler.to_excel(f'{outpath}/{key}.xlsx',index=None)
    sfpc_list.append(styler)
sfpc_list[0]

if HAS_LABEL:
    metric='fp'
    threshold=.5
    key=list(rl_dict.keys())[0]

    print(f"{key} - {metric}")
    comps = rl_dict[key]['rl'].comps
    match_score_counts = comps.query(f"{metric} == 1")['match_score'].value_counts()
    modified_code_counts = (
        comps
        .query(f"match_score > {threshold} and {metric} == 1")
        .merge(ds['ds2']['datasource']
                .reset_index()
                [['ds2_unique_pk','ds2_sample_modified_code']])
        [['match_score','ds2_sample_modified_code']]
        .value_counts())
    print(f"""\nMatch Scores ({metric}): 
{match_score_counts}""")

    print(f"""\nSample Modified Codes ({metric}):
{modified_code_counts}""") 

    rl_dict[key]['rl'].comps[[metric,'match_score']].groupby('match_score').agg({metric:'sum'}).plot()

# setup a list of thresholds for each record linkage
MATCH_SCORE_THRESHOLD = [.60]
for thresh,key in zip(MATCH_SCORE_THRESHOLD,rl_dict):
    print(f'\nRecord Linkage: {key}, Threshold: {thresh}')
    rl_dict[key]['rl'].set_matches(f"match_score >= {thresh} and match_pred == 1")
    rl_dict[key]['rl_output'] = rl_dict[key]['rl'].process_duplicates()
    rl_dict[key]['rl_output'] = rl_dict[key]['rl_output'].rename({'match_score':f'{key}_match_score'}).to_pandas()

rl_output_list = [rl_dict[key]['rl_output'] for key in rl_dict]
rl_output_all = None
for i,df in enumerate(rl_output_list):
    if i == 0:
        rl_output_all = df
    else:
        rl_output_all = rl_output_all.merge(df,how='outer')
rl_output_all

all_recs_list = [rl_output_all] + [ds[d]['datasource'][[ds[d]['dataset_key']]].reset_index(drop=True) for d in ds.keys()]
all_recs = None
for i,df in enumerate(all_recs_list):
    if i == 0:
        all_recs = df
    else:
        all_recs = all_recs.merge(df,how='outer')
all_recs = pl.from_pandas(all_recs).with_row_index('index',1)
all_recs = all_recs.sort(by='index')
all_recs

for key in rl_dict:
    print(f'\nRecord Linkage: {key}')
    ds1_key = rl_dict[key]['rl_ds1_dataset_key']
    ds2_key = rl_dict[key]['rl_ds2_dataset_key']
    match_score_fld = f"{key}_match_score"
    
    all_recs = RecordLinkage.assign_duplicates_same_index2(all_recs,ds1_key)
    all_recs = RecordLinkage.assign_duplicates_same_index2(all_recs,ds2_key)
    all_recs = (all_recs
                .filter(pl.any_horizontal(cs.exclude('index').is_not_null()))
                .sort(by='index'))

os.makedirs('rl',exist_ok=True)
all_recs_parquet_path = 'rl/all_recs.parquet'
all_recs.write_parquet(all_recs_parquet_path)
print(f'Data frame written to {all_recs_parquet_path}')

all_recs

isc_ids_df = RecordLinkage.generate_isc_ids(all_recs,'index')
print(f"Number of ISC_ID Records: {isc_ids_df.height:,}")

print(f"All Recs Dataframe Rows: {all_recs.height:,}")
print(f"All Recs Dataframe Unique Keys: {all_recs['index'].n_unique():,}")

all_recs_isc_id = (all_recs
                   .join(isc_ids_df,on='index')
                   .drop('index')
                   .unique())
print(f"All Recs ISC_ID Dataframe Rows: {all_recs_isc_id.height:,}")
print(f"All Recs ISC_ID Dataframe Unique Keys: {all_recs_isc_id['isc_id'].n_unique():,}")

os.makedirs('rl',exist_ok=True)
all_recs_isc_id_parquet_path = 'rl/all_recs_isc_id.parquet'
all_recs_isc_id.write_parquet(all_recs_isc_id_parquet_path)
print(f'Data frame written to {all_recs_isc_id_parquet_path}')

all_recs_isc_id

match_score_fields = [f'{k}_match_score' for k in rl_dict]
matches_isc_id = (all_recs_isc_id
                  .filter(~pl.all_horizontal([all_recs_isc_id[col].is_null() 
                                              for col in match_score_fields]))
                  .unique()
                  )

os.makedirs('rl',exist_ok=True)
matches_parquet_path = 'rl/matches_isc_id.parquet'
matches_isc_id.write_parquet(matches_parquet_path)
print(f'Data frame written to {matches_parquet_path}')

matches_isc_id

# get the counts by score for matches
for col in matches_isc_id.columns:
    if col.endswith('match_score'):
        print(matches_isc_id[col].value_counts().sort(by=col))

non_extra_samples = pl.from_pandas(ds2.query("ds2_sample_modified_code != 'Extra sample not dervived from main dataset'"))
non_extra_samples_id_counts = non_extra_samples['ds2_index'].n_unique()
all_sample_id_counts = ds2['ds2_index'].nunique()

matches_by_score_counts = matches_isc_id['ds1_ds2_match_score'].value_counts().sort(by='count',descending=True).to_pandas()

matches_id_counts = matches_isc_id['ds2_index'].n_unique()

extra_sample_match_id_counts = (matches_isc_id[['ds2_index']]
                                .join(pl.from_pandas(ds2),
                                      on='ds2_index')
                                .filter(pl.col('ds2_sample_modified_code') \
                                    == 'Extra sample not dervived from main dataset')
                                ['ds2_index'].n_unique())

non_extra_samples_not_matched = non_extra_samples.join(matches_isc_id,on='ds2_index',how='anti')
non_extra_samples_not_matched_id_counts = non_extra_samples_not_matched['ds2_index'].n_unique()

non_extra_samples_not_matched_modified_code_counts = \
    (non_extra_samples_not_matched['ds2_sample_modified_code']
     .value_counts()
     .sort(by=['count'],descending=True).to_pandas())

print(f'Number of Ids from Sample that are In Main Dataset: \
{non_extra_samples_id_counts:,}')

print(f'Number of All Ids from Sample: {all_sample_id_counts:,}')
match_perc = round((matches_id_counts / non_extra_samples_id_counts),3) * 100

print(f'Number of Main Ids Matched with Sample Dataset: \
{matches_id_counts:,} ({ match_perc :0.3}%)')

print(f'Number of Extra Sample Ids Matched between Datasets: \
{extra_sample_match_id_counts:,}')

main_ids_not_matched = non_extra_samples_id_counts - matches_id_counts
main_ids_not_matched_perc = \
    round((main_ids_not_matched / non_extra_samples_id_counts),3) * 100
print(f"Number of Main Ids Not Matched With Sample Dataset: \
{main_ids_not_matched:,} ({main_ids_not_matched_perc:0.3}%)")

print(f"""
Records in Main Not Matched with Sample by Modified Code:
{non_extra_samples_not_matched_modified_code_counts}      
""")


review_flds = ['first_name_clean','last_name_clean']
sample_frac = .01
EXPORT_REVIEW_DATA = True
review_list = [{"df":matches_isc_id,"key":None}] + \
    [{"df":RecordLinkage.get_review_data(ds[d]['datasource'],
                     prefix=d,
                     index_fld=ds[d]['dataset_key'],
                     review_flds=review_flds
                     ),
      "key":ds[d]['dataset_key']} for d in ds.keys()]
review_df = None
for i,d in enumerate(review_list):
    if i == 0:
        review_df = d['df']
    else:
        review_df = review_df.join(d['df'],how='left',on=d['key'])

review_df = review_df.sample(fraction=sample_frac).unique().sort(by='isc_id')
if EXPORT_REVIEW_DATA:
    os.makedirs('review',exist_ok=True)
    _timestamp = datetime.strftime(datetime.now(),format='%Y%m%d_%H%M%S')
    review_df.write_csv(f'review/review_data_{_timestamp}.csv')
review_df

