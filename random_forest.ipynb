{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from random_forest import RandomForest\n", "from recordlinkage.datasets import binary_vectors\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import confusion_matrix, f1_score, classification_report\n", "from sklearn.model_selection import train_test_split, GridSearchCV\n", "import pickle\n", "from joblib import dump, load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model= 'name_race_gender_exact_name'\n", "dte = '20250602'\n", "file = f'{model}_{dte}'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(f'ml/data/{file}.csv',index_col=['ds1_unique_pk','ds2_unique_pk'])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['true_match'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# all true links\n", "all_links = df.query(\"true_match == 1\").index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data minus label\n", "X = df.drop('true_match',axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# labels\n", "y = df[['true_match']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X,y,test_size=.2,stratify=y)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# training true links\n", "y_train_links = y_train.query(\"true_match == 1\").index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test true links\n", "y_test_links = y_test.query(\"true_match == 1\").index"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Train Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# random forest model\n", "rf = RandomForest(n_estimators=200)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# training predictions\n", "train_pred = rf.fit_predict(X_train,y_train_links)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# add the true match label and match prediction\n", "X_train = rf.set_results(X_train,y_train_links,'true_match',train_pred,'match_pred')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["confusion_matrix(X_train['true_match'].values,X_train['match_pred'].values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(classification_report(X_train['true_match'].values,X_train['match_pred'].values))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f1_score(X_train['true_match'].values,X_train['match_pred'].values)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Predictions on Test Set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test predictions\n", "test_pred = rf.predict(X_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# add the true match label and match prediction\n", "X_test = rf.set_results(X_test,y_test_links,'true_match',test_pred,'match_pred')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["confusion_matrix(X_test['true_match'].values,X_test['match_pred'].values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(classification_report(X_test['true_match'].values,X_test['match_pred'].values))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f1_score(X_test['true_match'].values,X_test['match_pred'].values)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Save Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dump(rf, f'ml/models/{file}.joblib')"]}], "metadata": {"kernelspec": {"display_name": "reclink", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}