# Record Linkage Code
Created by: <PERSON>

This code is used to link records from different data sources using information such as the persons name, race, gender and address coordinates.  The data is first preprocessed by cleaning and phonetically encoding the names, cleaning categories such as race and gender so both data sources use the same category values.  Next the data is indexed using a sorted neighborhood algorithm using a combination of the encoded names and the birth date to find potential matching candidates and also accounting for slight mispellings in the name. The results are the candidates that will be used for comparison matching using all the other fields that are common between both datasets.

The comparison section will perform different types of matching against the common fields specified in **compare_flds variables** and calculate a match score which can be defined using a weighting to give more weight to more important fields. The weights can also just be set to None where it will just divide the number of matched fields by the total fields. This will generate a table of match results, this table is passed through the Random Forest classification model to classify predictions.  You can review how the match scores and predictions vary and determine the score threshold to keep matches.

Once the matched dataset is determined any duplicates where one key is matched to two or more keys from a different data sources are analyzed and the key combination with the highest score are kept and the others are discarded.  If there are duplicates with the same score they are given the same ISC_ID meaning that these two records while they are assigned to more than one key likely represent the same person.  A dataset is generated with all the matching combinations but also includes the records from the original data sources since they may be needed for other analysis like generating comparison groups.

All Python libraries needed are in **requirements.txt** to install it is assumed you have a python envirment setup and then you can just run the command:
```
pip install -r requirements.txt
```

## Load Data

### Create a Unique Key
There may be cases where the dataset has more than one record per person, such as when a person has more than one address.  The function **create_unique_key** will sort a dataset by an existing key and use that exisiting key to append a number based on **IDX_COUNT** to make it a unique key and save that to a new field.  **PREV_KEY** will track the last key value that was processed and **IDX_COUNT** will track the current count to append the number based on how many duplicate keys are there.

### Rename Columns
This function will prepend text such as "ds1_" for the ds1 variable to distguish the columns from other data sources which will help further down in the record linkage process.  The index will be set and key fields used for record linkage will be identified and renamed into a standard naming convention to easily identify the different fields.

## [Preprocessing](https://recordlinkage.readthedocs.io/en/latest/ref-preprocessing.html)

Setup the data sources dictionary for every data source that will be used in the record linkage, specify the data source DataFrame variable from the previous steps, the name fields that will be preprocessed and the name for the unique key and secondary key. The cleaning will be done on the **name_fields** for each datasource where it is run through the Record Linkage Toolkit's cleaning methods and it wil also get the phonetic Soundex code from the cleaned version of the name.

## Get Record Linkage Combinatons

Call **get_rl_combinations** passing in the data source keys you want to use in the records linkage, the data source dictionary and optionally the main dataset key that will control how the record linkage combinations will be applied.  If a main dataset key is provided that data source will be used in the record linkage with all the datas sources in the data source keys.  If no main data set key is provided all combinations of the data source keys will be used in the record linkage.

## [Indexing](https://recordlinkage.readthedocs.io/en/latest/ref-index.html)

Use a sorted neighborhood index on a combination of the Soundex first name and birthdate and Soundex last name and birthdate using the window size to account for misspellings in the first or last name to get the neighboring records.

## [Compare Fields](https://recordlinkage.readthedocs.io/en/latest/ref-compare.html)
Define a list of dictionaries that will have the following keys:
* **ds1_fld** *Required*: Name of the field in dataset 1.
* **ds2_fld** *Required*: Name of the field in dataset 2.
* **compare_type** *Required*: Comparison method to use (exact, string, date or geo).
* **match_fldname** *Required*: Name for the field name in the comparison matched output.
* **weight** *Required*: Value between in 0 and 1.  The sum of all the fields weight values must equal to 1.  Can be set to None if weighting will not be used.
* **method**: String similarity method (only when ```compare_type == 'string'```).  Values can be jarowinkler, jaro, levenshtein, damerau_levenshtein, qgram or cosine. 
* **method**: Method for comparing geographic distance (only when ```compare_type == 'geo'```).  Values can be linear, step, exp, guass or squared.
* **thresh**: Threshold of what is considered a match (only when ```compare_type == 'string'|'geo'```).  Values can range from 0 to 1.
* **left_on_lat**: Latitude field for dataset 1.
* **left_on_lng**: Longitude field for dataset 1.
* **right_on_lat**: Latitude field for dataset 2.
* **right_on_lng**: Longitude field for dataset 2.

## [Classification](https://recordlinkage.readthedocs.io/en/latest/ref-classifiers.html)
Load the Random Forest classification model, this will use the comparisons stored in the **comps** data frame to make predictions and the predictions will be stored in the **preds** variable. A **MATCH_SCORE** is calcuated which is either a weighted sum or the number of matched fields divided by the total fields based on how the weight variable was set. The predictions are stored in the **MATCH_PRED** field. If this is a labeled datatset and the records are a true match this is stored in the **TRUE_MATCH** field.

## [Evaluation](https://recordlinkage.readthedocs.io/en/latest/ref-evaluation.html)

Check how the match scores and predictions vary.  This will typically show that higher match scores have more predictions as matches.  Since this is a labeled dataset we can also run the confusion matrix and examine the false positive and false negatives.

### Set Matches & Check for Duplicates
Call **set_matches** passing in the **MATCH_SCORE_THRESHOLD** of what is to be considered a match, this will store a seperate data frame of just records that are set as matches. Next **process_duplicates**  is called on the matched dataset and this  will process any duplicates between the data sources based on the key values.  If there is a key from one data source that is matched with two or more different keys from the other data sources the records with the highest score is kept and the others are removed from the dataset.

## Deduplication
There may be cases were there is a key from one data source is matched two or more keys from another data source that have the same match score. These will be treated as the same person and be assigned the same ISC_ID in order to deduplicate the data set and not assign mulitple ISC_IDs to the same person.

## Generate ISC_IDS
**ISC_IDs** are the ids generated in place of the PII.  Call the ```RecordLinkage.generate_isc_ids(df, id_field)``` passing in the data frame and the unique id field, which results in a dataframe that includes the ISC_ID field.
 


