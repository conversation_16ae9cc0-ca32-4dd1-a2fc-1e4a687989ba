{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Deduplication Code\n", "Created by: <PERSON>\n", "\n", "This notebook shows how to deduplicate a dataset.  Deduplication occures when you want to examine one data source and see if there are duplicates within that data source itself.  The goal is to find records that have different keys but represent the same person.  This code will find what records are most likely duplicates, but further work with the data owner may be needed one how to resolve these duplicates."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from joblib import load\n", "import json\n", "import numpy as np\n", "import os\n", "import plotly.express as px\n", "import polars as pl\n", "import pprint\n", "from record_linkage import RecordLinkage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# flag if there is a label with this dataset that can be used for training an assessing accuracy\n", "HAS_LABEL = True\n", "# name of the label field\n", "LABEL_FLD = 'true_match'\n", "# models folder\n", "MODELS_FOLDER = 'ml/models'\n", "# dedup flag\n", "DEDUP = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Preprocessing](https://recordlinkage.readthedocs.io/en/latest/ref-preprocessing.html)\n", "\n", "Setup the data sources dictionary for every data source that will be used in the record linkage, specify the data source, DataFrame variable, the name fields that will be preprocessed and the name for the unique key, dataset_key and alternate_key.  The alternate_key is setup if there is another key that can be used as a reference, but the dataset_key is what will be used to generate the unique key. The cleaning will be done on the **name_fields** for each datasource where it is run through the Record Linkage Toolkit's cleaning methods and it wil also get the phonetic Soundex code from the cleaned version of the name."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1_file_path = 'data/main_fake_dataset_100k.csv'\n", "name_fields = ['first_name','middle_name','last_name']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rl = RecordLinkage(dedup=DEDUP)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1 = pl.read_csv(ds1_file_path)\n", "ds1_dataset_key = 'index'\n", "ds1 = RecordLinkage.assign_unique_key(df=ds1,\n", "                                key_fld=ds1_dataset_key,\n", "                                unique_key_name=rl.unique_key_name)\n", "ds1 = RecordLinkage.rename_columns(ds1,\n", "                             first_name='name_first',\n", "                             middle_name='name_middle',\n", "                             last_name='name_last',\n", "                             birth_date='birth_date',\n", "                             gender='gender',\n", "                             race='bifsg_race',\n", "                             latitude='latitude',\n", "                             longitude='longitude')\n", "\n", "ds1 = ds1.to_pandas()\n", "ds1['birth_date'] = ds1['birth_date'].astype('datetime64[ns]')\n", "\n", "ds1_ds = RecordLinkage.create_dataset_dict('ds1', ds1, ds1_dataset_key, rl.unique_key_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1_ds['ds1']['datasource']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get Record Linkage Combinatons\n", "\n", "Call **get_rl_combinations** passing in the data source dictionary (**ds**) and optionally the main dataset key (**main_ds**) that will control how the record linkage combinations will be applied.  If a main dataset key is provided that data source will be used in the record linkage with all the data sources in the ds keys.  \n", "* ds keys = [ds1,ds2,ds3]\n", "* main_ds = 'ds1'\n", "* combinations = [(ds1,ds2),(ds1,ds3)]\n", "\n", "If no main data set key is provided all combinations of the data source keys will be used in the record linkage. \n", "* ds keys = [ds1,ds2,ds3]\n", "* main_ds = None\n", "* combinations = [(ds1,ds2),(ds1,ds3),(ds2,ds3)]\n", "\n", "If the **main_ds** key is provided and the **dedup** parameter is True this will perform a deduplication process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds = {**ds1_ds}\n", "rl_dict = RecordLinkage.get_rl_combinations(ds=ds,main_ds='ds1',dedup=DEDUP)\n", "pprint.pprint(rl_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Indexing](https://recordlinkage.readthedocs.io/en/latest/ref-index.html)\n", "\n", "Use a sorted neighborhood index on a combination of the Soundex first name and birthdate and Soundex last name and birthdate using the window size to account for misspellings in the first or last name to get the neighboring records."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for rl_key in rl_dict.keys():\n", "    RecordLinkage.run_sorted_neighborhood_indexing(rl_dict[rl_key],ds,window_size=9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Compare Fields](https://recordlinkage.readthedocs.io/en/latest/ref-compare.html)\n", "Define a list of dictionaries that will have the following keys:\n", "* **ds1_fld** *Required*: Name of the field in dataset 1.\n", "* **ds2_fld** *Required*: Name of the field in dataset 2.\n", "* **compare_type** *Required*: Comparison method to use (exact, string, date or geo).\n", "* **match_fldname** *Required*: Name for the field name in the comparison matched output.\n", "* **weight** *Required*: Value between in 0 and 1.  The sum of all the fields weight values must equal to 1.  Can be set to None if weighting will not be used.\n", "* **method**: String similarity method (only when ```compare_type == 'string'```).  Values can be jar<PERSON><PERSON><PERSON>, jaro, levenshtein, damerau_levenshtein, qgram or cosine. \n", "* **method**: Method for comparing geographic distance (only when ```compare_type == 'geo'```).  Values can be linear, step, exp, guass or squared.\n", "* **thresh**: Threshold of what is considered a match (only when ```compare_type == 'string'|'geo'```).  Values can range from 0 to 1.\n", "* **left_on_lat**: Latitude field for dataset 1.\n", "* **left_on_lng**: Longitude field for dataset 1.\n", "* **right_on_lat**: Latitude field for dataset 2.\n", "* **right_on_lng**: Longitude field for dataset 2."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set Comparison Fields\n", "Set the different combination of fields that will be used in the comparison matching.  Currently there are the following comparison field combinations.\n", "*   **name_race_gender** - Weighted comparision using the first, middle and last names, the date of birth and the race and gender fields.\n", "*   **name_race_gender_coords** - Weighted comparision using the same fields as **name_race_gender** with the addition of the address coordinates.\n", "*   **name_race_gender_exact_name** - Weighted comparison using the same fields as **name_race_gender** with the only addition of performing an exact match on the name fields in addition to the string similarity methods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('compare_methods.json','r') as cm:\n", "    compare_methods = json.load(cm)\n", "compare_methods.keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Assign <PERSON><PERSON><PERSON><PERSON> to Record Linkage Dictionary\n", "For each record linkage being performed assign the comparision name that will be used for the matching."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rl_dict['ds1_ds1']['compare_name'] = \"name_race_gender_exact_name\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Records\n", "Run comparison matching for each record linkage by calling **compare_records** passing in the comparison field mapping."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for rl_key in rl_dict:\n", "    print(rl_key)\n", "    compare_name = rl_dict[rl_key]['compare_name']\n", "    compare_flds = compare_methods[compare_name]\n", "    \n", "    print(f'\\nRecord Linkage: {rl_key}')\n", "    match_flds = [x['match_fldname'] for x in compare_flds]\n", "    \n", "    rl_dict[rl_key]['compare_fields'] = (\n", "        RecordLinkage.set_comparison_fields(compare_flds,\n", "        rl_dict[rl_key]['rl_ds1'],\n", "        rl_dict[rl_key]['rl_ds2'])\n", "        )\n", "    \n", "    rl_dict[rl_key]['rl'].compare_records(rl_dict[rl_key]['compare_fields'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Classification](https://recordlinkage.readthedocs.io/en/latest/ref-classifiers.html)\n", "Load the Random Forest classification models, this will use the comparisons stored in the **comps** data frame to make predictions and the predictions will be stored in the record_linkage object's **preds** variable. A **match_score** is calcuated which is either a weighted sum or the number of matched fields divided by the total fields based on how the weight variable was set in the comparison fields. The predictions are stored in the **match_pred** field. If this is a labeled datatset a field will be created based on what is passed in the **LABEL_FLD** variable and will indicated if the records are a match with what was predicted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Classification Models\n", "* Load the classification models that are saved in the **models** directory.\n", "* Based on the **compare_name** that was set set the **model** key to point to the correct model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load random forest classification models\n", "rf_name_dob_gender_race = load(f'{MODELS_FOLDER}/rf_model_name_dob_gender_race_20231016.joblib')\n", "rf_comps_name_race_gender_exact_name = load(f'{MODELS_FOLDER}/rf_model_name_race_gender_exact_name_20250107.joblib')\n", "rf_name_dob_gender_race_coord = load(f'{MODELS_FOLDER}/rf_model_name_dob_gender_race_coord_20231016.joblib')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key in rl_dict:\n", "    if rl_dict[key]['compare_name'] == 'name_race_gender_coords':\n", "        rl_dict[key]['model'] = rf_name_dob_gender_race_coord\n", "    elif rl_dict[key]['compare_name'] == 'name_race_gender_exact_name':\n", "        rl_dict[key]['model'] = rf_comps_name_race_gender_exact_name\n", "    elif rl_dict[key]['compare_name'] == 'name_race_gender':\n", "        rl_dict[key]['model'] = rf_name_dob_gender_race"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Make Predictions\n", "* Call the **predict** method to make predictions using the assigned model for each record linkage combination.\n", "* Calculate the **match_score** variable.\n", "* If this is a labeled dataset\n", "  * Calculate the **LABEL_FLD** to indicate a true match.\n", "  * Calculate fields to track true positive (**tp**), false negative (**fn**), true negative (**tn**) and false positive (**fp**) metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key in rl_dict:\n", "    print(f'\\nRecord Linkage: {key}')\n", "    model = rl_dict[key]['model']\n", "    rl_dict[key]['rl'].predict(model)\n", "    rl_dict[key]['rl'].calc_match_score()\n", "    comps = rl_dict[key]['rl'].comps\n", "    if HAS_LABEL:\n", "        ds_key1 = rl_dict[key]['rl'].ds1_dataset_key\n", "        ds_key2 = rl_dict[key]['rl'].ds2_dataset_key\n", "        rl_dict[key]['rl'].comps[LABEL_FLD] = np.where(rl_dict[key]['rl'].comps[ds_key1] == rl_dict[key]['rl'].comps[ds_key2],1,0)\n", "        rl_dict[key]['rl'].comps['tp'] = np.where(((comps[LABEL_FLD] == 1) & (comps['match_pred'] == 1)),1,0)\n", "        rl_dict[key]['rl'].comps['fn'] = np.where(((comps[LABEL_FLD] == 1) & (comps['match_pred'] == 0)),1,0)\n", "        rl_dict[key]['rl'].comps['tn'] = np.where(((comps[LABEL_FLD] == 0) & (comps['match_pred'] == 0)),1,0)\n", "        rl_dict[key]['rl'].comps['fp'] = np.where(((comps[LABEL_FLD] == 0) & (comps['match_pred'] == 1)),1,0)\n", "        rl_dict[key]['rl'].comps = comps\n", "    del comps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# [Evaluation](https://recordlinkage.readthedocs.io/en/latest/ref-evaluation.html)\n", "\n", "Check how the match scores and predictions vary.  This will typically show that higher match scores have more predictions as matches.  Since this is a labeled dataset we can also run the confusion matrix and examine the false positive and false negatives."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Predictions by Score\n", "* Get a summary of the count of each prediction class (0 or 1) for each match score.\n", "* Print which scores have counts for both classes\n", "* Visualize the count of records by match score using seperate color for each prediction class to show how predictions change as the match score changes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key in rl_dict:\n", "    print(f'\\nRecord Linkage: {key}')\n", "    print(rl_dict[key]['rl'].get_score_pred_summary())\n", "    \n", "    print(rl_dict[key]['rl'].scores_pred.query('match_score >= .5'))\n", "    print(f\"Match scores {rl_dict[key]['rl'].scores_review} have predictions for matches and non matches.\")\n", "    \n", "    \n", "    scores_pred = rl_dict[key]['rl'].scores_pred.explode(['predictions','number_rows'])\n", "    fig = px.line(scores_pred, \n", "                x=\"match_score\", \n", "                y=\"number_rows\", \n", "                color='predictions',\n", "                color_discrete_sequence=['red','green'],\n", "                title=f'Predictions by Score for {key}',\n", "                labels={\n", "                        \"match_score\": \"Match Score\",\n", "                        \"number_rows\": \"Number of Records\",\n", "                        \"predictions\": \"Prediction\"\n", "                    },\n", "                )\n", "    fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set Matches & Check for Duplicates\n", "* Call **set_matches** passing in the **MATCH_SCORE_THRESHOLD** of what is to be considered a match, this will create a seperate data frame of just records that are set as matches.  **MATCH_SCORE_THRESHOLD** is a list so you specify different thresholds to use for each record linkage being performed. \n", "* Call **process_duplicates**  on the matched dataset and to process any duplicates between the data sources based on the key values. If there is a key from one data source that is matched with two or more different keys from the other data sources the records with the highest score is kept and the others are removed from the dataset. The final output is saved in a key called **rl_output**."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# setup a list of thresholds for each record linkage\n", "MATCH_SCORE_THRESHOLD = [.95]\n", "for thresh,key in zip(MATCH_SCORE_THRESHOLD,rl_dict):\n", "    print(f'\\nRecord Linkage: {key}, Threshold: {thresh}')\n", "    rl_dict[key]['rl'].set_matches(f\"match_score >= {thresh} and match_pred == 1\")\n", "    rl_dict[key]['rl_output'] = rl_dict[key]['rl'].process_duplicates()\n", "    rl_dict[key]['rl_output'] = rl_dict[key]['rl_output'].rename({'match_score':f'{key}_match_score'}).to_pandas()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get Potential Duplicates with Different Keys"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rl_output_all = None\n", "for i,key in enumerate(rl_dict):\n", "    ds1_key = rl_dict[key]['rl_ds1_dataset_key']\n", "    ds2_key = rl_dict[key]['rl_ds2_dataset_key']\n", "    if i == 0:\n", "        rl_output_all = rl_dict[key]['rl_output']\n", "    else:\n", "        rl_output_all = rl_output_all.merge(rl_dict[key]['rl_output'],how='outer')  \n", "    rl_output_all = rl_output_all.query(f\"{ds1_key} != {ds2_key}\")\n", "rl_output_all"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "reclink", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}